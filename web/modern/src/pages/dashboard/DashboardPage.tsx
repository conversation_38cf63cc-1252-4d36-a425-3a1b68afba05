import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { ColumnDef } from '@tanstack/react-table'
import { useAuthStore } from '@/lib/stores/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { api } from '@/lib/api'
import { DataTable } from '@/components/ui/data-table'
import { ResponsivePageContainer, ResponsiveSection } from '@/components/ui/responsive-container'
import { AdaptiveGrid } from '@/components/ui/adaptive-grid'
import { useResponsive } from '@/hooks/useResponsive'
import { formatNumber, cn } from '@/lib/utils'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  <PERSON><PERSON>hart,
  Bar,
  Legend,
} from 'recharts'

export function DashboardPage() {
  const { user } = useAuthStore()
  const { isMobile, isTablet } = useResponsive()
  const isAdmin = useMemo(() => (user?.role ?? 0) >= 10, [user])

  // date range defaults: last 7 days (inclusive)
  const fmt = (d: Date) => d.toISOString().slice(0, 10)
  const today = new Date()
  const last7 = new Date(today)
  last7.setDate(today.getDate() - 6)

  const [fromDate, setFromDate] = useState(fmt(last7))
  const [toDate, setToDate] = useState(fmt(today))
  const [dashUser, setDashUser] = useState<string>('all')
  const [userOptions, setUserOptions] = useState<Array<{ id: number; username: string; display_name: string }>>([])
  const [loading, setLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)

  type Row = { day: string; model_name: string; request_count: number; quota: number; prompt_tokens: number; completion_tokens: number }
  const [rows, setRows] = useState<Row[]>([])

  const totals = useMemo(() => {
    return rows.reduce(
      (acc, r) => {
        acc.requests += r.request_count || 0
        acc.quota += r.quota || 0
        acc.tokens += (r.prompt_tokens || 0) + (r.completion_tokens || 0)
        return acc
      },
      { requests: 0, quota: 0, tokens: 0 }
    )
  }, [rows])

  const loadUsers = async () => {
    if (!isAdmin) return
    const res = await api.get('/user/dashboard/users')
    if (res.data?.success) {
      setUserOptions(res.data.data || [])
    }
  }

  const loadStats = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      params.set('from_date', fromDate)
      params.set('to_date', toDate)
      if (isAdmin) {
        params.set('user_id', dashUser || 'all')
      }
      const res = await api.get('/user/dashboard?' + params.toString())
      const { success, data, message } = res.data
      if (success) {
        setRows(data || [])
        setLastUpdated(new Date().toLocaleTimeString())
      } else {
        console.warn('dashboard fetch failed', message)
        setRows([])
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isAdmin) loadUsers()
    // load initial stats
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAdmin])
  useEffect(() => {
    loadStats()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Presets
  const applyPreset = (preset: 'today' | '7d' | '30d') => {
    const today = new Date()
    const start = new Date(today)
    if (preset === 'today') start.setDate(today.getDate())
    if (preset === '7d') start.setDate(today.getDate() - 6)
    if (preset === '30d') start.setDate(today.getDate() - 29)
    setFromDate(fmt(start))
    setToDate(fmt(today))
    // defer fetch until user clicks Apply/Refresh to avoid surprise reloads
  }

  // Build time-series by day totals
  const days = useMemo(() => Array.from(new Set(rows.map(r => r.day))).sort(), [rows])
  const timeSeries = useMemo(() => {
    // Aggregate across models per day
    const map: Record<string, { date: string; requests: number; quota: number; tokens: number }> = {}
    for (const r of rows) {
      if (!map[r.day]) map[r.day] = { date: r.day, requests: 0, quota: 0, tokens: 0 }
      map[r.day].requests += r.request_count || 0
      map[r.day].quota += r.quota || 0
      map[r.day].tokens += (r.prompt_tokens || 0) + (r.completion_tokens || 0)
    }
    return Object.values(map).sort((a, b) => a.date.localeCompare(b.date))
  }, [rows])

  // Model stacked bar per day
  const uniqueModels = useMemo(() => Array.from(new Set(rows.map(r => r.model_name))), [rows])
  const stackedByTokens = useMemo(() => {
    const map: Record<string, Record<string, number>> = {}
    for (const d of days) map[d] = {}
    for (const r of rows) {
      const totalTokens = (r.prompt_tokens || 0) + (r.completion_tokens || 0)
      map[r.day][r.model_name] = (map[r.day][r.model_name] || 0) + totalTokens
    }
    return days.map(d => ({ date: d, ...(map[d] || {}) }))
  }, [rows, days])

  const columns: ColumnDef<Row>[] = [
    { header: 'Day', accessorKey: 'day' },
    { header: 'Model', accessorKey: 'model_name' },
    { header: 'Requests', accessorKey: 'request_count' },
    { header: 'Quota', accessorKey: 'quota' },
    { header: 'Prompt Tokens', accessorKey: 'prompt_tokens' },
    { header: 'Completion Tokens', accessorKey: 'completion_tokens' },
  ]

  // Summaries & insights
  const dailyAgg = useMemo(() => {
    const m: Record<string, { date: string; requests: number; quota: number; tokens: number }> = {}
    for (const r of rows) {
      if (!m[r.day]) m[r.day] = { date: r.day, requests: 0, quota: 0, tokens: 0 }
      m[r.day].requests += r.request_count || 0
      m[r.day].quota += r.quota || 0
      m[r.day].tokens += (r.prompt_tokens || 0) + (r.completion_tokens || 0)
    }
    return Object.values(m).sort((a, b) => a.date.localeCompare(b.date))
  }, [rows])

  const todayAgg = dailyAgg.length ? dailyAgg[dailyAgg.length - 1] : { requests: 0, quota: 0, tokens: 0 }
  const prevAgg = dailyAgg.length > 1 ? dailyAgg[dailyAgg.length - 2] : { requests: 0, quota: 0, tokens: 0 }
  const pct = (cur: number, prev: number) => (prev > 0 ? ((cur - prev) / prev) * 100 : 0)
  const requestTrend = pct(todayAgg.requests, prevAgg.requests)
  const quotaTrend = pct(todayAgg.quota, prevAgg.quota)
  const tokenTrend = pct(todayAgg.tokens, prevAgg.tokens)
  const avgCostPerRequest = todayAgg.requests ? todayAgg.quota / todayAgg.requests : 0
  const avgTokensPerRequest = todayAgg.requests ? todayAgg.tokens / todayAgg.requests : 0

  const byModel = useMemo(() => {
    const mm: Record<string, { model: string; requests: number; quota: number; tokens: number }> = {}
    for (const r of rows) {
      const key = r.model_name
      if (!mm[key]) mm[key] = { model: key, requests: 0, quota: 0, tokens: 0 }
      mm[key].requests += r.request_count || 0
      mm[key].quota += r.quota || 0
      mm[key].tokens += (r.prompt_tokens || 0) + (r.completion_tokens || 0)
    }
    return Object.values(mm)
  }, [rows])

  const efficiency = useMemo(() => {
    return byModel
      .map(m => ({
        model: m.model,
        requests: m.requests,
        avgCost: m.requests ? m.quota / m.requests : 0,
        avgTokens: m.requests ? m.tokens / m.requests : 0,
        efficiency: m.quota > 0 ? m.tokens / m.quota : 0, // tokens per quota unit
      }))
      .sort((a, b) => b.requests - a.requests)
  }, [byModel])

  const topModel = efficiency.length ? efficiency[0].model : ''
  const totalModels = efficiency.length

  const usagePatterns = useMemo(() => {
    const daily = dailyAgg
    let peakDay = ''
    let peakRequests = 0
    for (const d of daily) {
      if (d.requests > peakRequests) { peakRequests = d.requests; peakDay = d.date }
    }
    const avgDaily = daily.length ? Math.round(daily.reduce((s, d) => s + d.requests, 0) / daily.length) : 0
    const trend = todayAgg.requests > avgDaily ? 'Rising' : 'Declining'
    return { peakDay, avgDaily, trend }
  }, [dailyAgg, todayAgg.requests])

  const recommendations = useMemo(() => {
    if (!efficiency.length) return [] as string[]
    const sortedByCost = [...efficiency].sort((a, b) => b.avgCost - a.avgCost)
    const highCost = sortedByCost[0]
    const mostEff = [...efficiency].sort((a, b) => b.efficiency - a.efficiency)[0]
    const dailyCost = todayAgg.quota // using current day as a simple proxy
    const monthlyProjection = Math.round((dailyAgg.length ? (dailyAgg.reduce((s, d) => s + d.quota, 0) / dailyAgg.length) : 0) * 30)
    return [
      highCost ? `High cost model detected: ${highCost.model} has high cost/request (${formatNumber(highCost.avgCost)}). Consider optimizing prompts or switching models.` : '',
      mostEff ? `Most efficient model: ${mostEff.model} with ${formatNumber(mostEff.efficiency)} tokens per quota.` : '',
      `Monthly projection based on recent daily average: ${formatNumber(monthlyProjection)} quota.`,
    ].filter(Boolean)
  }, [efficiency, todayAgg.quota, dailyAgg])

  if (!user) {
    return <div>Please log in to access the dashboard.</div>
  }

  return (
    <ResponsivePageContainer
      title="Dashboard"
      description="Monitor your API usage and account statistics"
    >
      {/* Account Overview Cards */}
      <ResponsiveSection title="Account Overview">
        <AdaptiveGrid
          cols={{ default: 1, sm: 2, lg: 3 }}
          gap={isMobile ? "md" : "lg"}
        >
          <Card>
            <CardHeader className={cn(isMobile ? "pb-3" : "pb-4")}>
              <CardTitle className={cn(isMobile ? "text-base" : "text-lg")}>Account Info</CardTitle>
              <CardDescription className={cn(isMobile ? "text-xs" : "text-sm")}>
                Your account details
              </CardDescription>
            </CardHeader>
            <CardContent className={cn(isMobile ? "pt-0" : "")}>
              <div className={cn("space-y-2", isMobile ? "text-sm" : "")}>
                <p><strong>Username:</strong> {user.username}</p>
                <p><strong>Display Name:</strong> {user.display_name || 'Not set'}</p>
                <p><strong>Role:</strong> {isAdmin ? 'Admin' : 'User'}</p>
                <p><strong>Group:</strong> {user.group}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className={cn(isMobile ? "pb-3" : "pb-4")}>
              <CardTitle className={cn(isMobile ? "text-base" : "text-lg")}>Quota Usage</CardTitle>
              <CardDescription className={cn(isMobile ? "text-xs" : "text-sm")}>
                Your API usage statistics
              </CardDescription>
            </CardHeader>
            <CardContent className={cn(isMobile ? "pt-0" : "")}>
              <div className={cn("space-y-2", isMobile ? "text-sm" : "")}>
                <p><strong>Total Quota:</strong> {user.quota?.toLocaleString() || 'N/A'}</p>
                <p><strong>Used:</strong> {user.used_quota?.toLocaleString() || 'N/A'}</p>
                <p><strong>Remaining:</strong> {user.quota && user.used_quota ? (user.quota - user.used_quota).toLocaleString() : 'N/A'}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className={cn(isMobile ? "pb-3" : "pb-4")}>
              <CardTitle className={cn(isMobile ? "text-base" : "text-lg")}>Status</CardTitle>
              <CardDescription className={cn(isMobile ? "text-xs" : "text-sm")}>
                Account status
              </CardDescription>
            </CardHeader>
            <CardContent className={cn(isMobile ? "pt-0" : "")}>
              <div className={cn("space-y-2", isMobile ? "text-sm" : "")}>
                <p><strong>Status:</strong> {user.status === 1 ? 'Active' : 'Inactive'}</p>
                <p><strong>Email:</strong> {user.email || 'Not set'}</p>
              </div>
            </CardContent>
          </Card>
        </AdaptiveGrid>
      </ResponsiveSection>

      {/* Usage Overview Section */}
      <ResponsiveSection
        title="Usage Overview"
        description="Daily requests and quota by model"
        actions={
          <div className={cn(
            "flex gap-2",
            isMobile ? "flex-col w-full" : "items-center"
          )}>
            {lastUpdated && (
              <span className={cn(
                "px-2 py-1 rounded-full bg-accent/40 text-xs text-muted-foreground",
                isMobile ? "text-center" : ""
              )}>
                Updated: {lastUpdated}
              </span>
            )}
            <Button
              variant="outline"
              onClick={loadStats}
              disabled={loading}
              size="sm"
              className={cn(isMobile ? "w-full touch-target" : "")}
            >
              Refresh
            </Button>
          </div>
        }
        variant="card"
      >
        {/* Date Range and User Filter */}
        <div className={cn(
          "grid gap-3 mb-4",
          isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-12"
        )}>
          <div className={cn(isMobile ? "" : "md:col-span-3")}>
            <label className="text-xs block mb-1">From</label>
            <Input type="date" value={fromDate} onChange={(e)=>setFromDate(e.target.value)} />
          </div>
          <div className={cn(isMobile ? "" : "md:col-span-3")}>
            <label className="text-xs block mb-1">To</label>
            <Input type="date" value={toDate} onChange={(e)=>setToDate(e.target.value)} />
          </div>
          {isAdmin && (
            <div className={cn(isMobile ? "" : "md:col-span-3")}>
              <label className="text-xs block mb-1">User</label>
              <select className="h-9 w-full border rounded-md px-2 text-sm" value={dashUser} onChange={(e)=>setDashUser(e.target.value)}>
                <option value="all">All Users (Site-wide)</option>
                {userOptions.map(u => (
                  <option key={u.id} value={String(u.id)}>{u.display_name || u.username}</option>
                ))}
              </select>
              <div className="text-[11px] text-muted-foreground mt-1">As root, you can select up to 1 year of data.</div>
            </div>
          )}
          <div className={cn(
            isMobile ? "col-span-full" : "md:col-span-3",
            "flex items-end"
          )}>
            <div className={cn(
              "flex gap-2",
              isMobile ? "flex-col w-full" : "flex-row"
            )}>
              <div className={cn(
                "flex gap-1",
                isMobile ? "w-full" : ""
              )}>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => applyPreset('today')}
                  className={cn(isMobile ? "flex-1 text-xs" : "")}
                >
                  Today
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => applyPreset('7d')}
                  className={cn(isMobile ? "flex-1 text-xs" : "")}
                >
                  7 Days
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => applyPreset('30d')}
                  className={cn(isMobile ? "flex-1 text-xs" : "")}
                >
                  30 Days
                </Button>
              </div>
              <Button
                onClick={loadStats}
                disabled={loading}
                className={cn(isMobile ? "w-full touch-target" : "")}
              >
                Apply
              </Button>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <AdaptiveGrid
          cols={{ default: 1, sm: 2, lg: 4 }}
          gap={isMobile ? "sm" : "md"}
          className="mb-4"
        >
          <Card className="p-3">
            <div className="text-xs text-muted-foreground mb-1">Total Requests (Today)</div>
            <div className={cn("font-semibold", isMobile ? "text-xl" : "text-2xl")}>
              {formatNumber(todayAgg.requests)}
            </div>
            <div className={cn(
              "text-xs mt-1",
              requestTrend >= 0 ? 'text-green-600' : 'text-red-600'
            )}>
              {requestTrend >= 0 ? '+' : ''}{requestTrend.toFixed(1)}%
            </div>
          </Card>

          <Card className="p-3">
            <div className="text-xs text-muted-foreground mb-1">Total Quota (Today)</div>
            <div className={cn("font-semibold", isMobile ? "text-xl" : "text-2xl")}>
              {formatNumber(todayAgg.quota)}
            </div>
            <div className={cn(
              "text-xs mt-1",
              quotaTrend >= 0 ? 'text-green-600' : 'text-red-600'
            )}>
              {quotaTrend >= 0 ? '+' : ''}{quotaTrend.toFixed(1)}%
            </div>
          </Card>

          <Card className="p-3">
            <div className="text-xs text-muted-foreground mb-1">Total Tokens (Today)</div>
            <div className={cn("font-semibold", isMobile ? "text-xl" : "text-2xl")}>
              {formatNumber(todayAgg.tokens)}
            </div>
            <div className={cn(
              "text-xs mt-1",
              tokenTrend >= 0 ? 'text-green-600' : 'text-red-600'
            )}>
              {tokenTrend >= 0 ? '+' : ''}{tokenTrend.toFixed(1)}%
            </div>
          </Card>

          <Card className="p-3">
            <div className="text-xs text-muted-foreground mb-1">Avg Cost / Request</div>
            <div className={cn("font-semibold", isMobile ? "text-xl" : "text-2xl")}>
              {avgCostPerRequest ? avgCostPerRequest.toFixed(4) : '0'}
            </div>
            <div className="text-xs mt-1">
              Avg Tokens: {avgTokensPerRequest ? Math.round(avgTokensPerRequest) : 0}
            </div>
          </Card>
        </AdaptiveGrid>

        {/* Insights */}
        <AdaptiveGrid
          cols={{ default: 1, lg: 3 }}
          gap={isMobile ? "sm" : "md"}
          className="mb-6"
        >
          <Card className="p-3">
            <div className={cn("mb-2", isMobile ? "text-sm" : "text-sm")}>Model Usage Insights</div>
            <div className="text-xs text-muted-foreground">Most Used Model</div>
            <div className="text-base font-medium">{topModel || '-'}</div>
            <div className="text-xs text-muted-foreground mt-2">Active Models</div>
            <div className="text-base font-medium">{totalModels}</div>
          </Card>

          <Card className="p-3">
            <div className={cn("mb-2", isMobile ? "text-sm" : "text-sm")}>Performance Metrics</div>
            <div className="text-xs text-muted-foreground">Avg Tokens/Req</div>
            <div className="text-base font-medium">{avgTokensPerRequest ? Math.round(avgTokensPerRequest) : 0}</div>
            <div className="text-xs text-muted-foreground mt-2">Throughput (req/day)</div>
            <div className="text-base font-medium">{todayAgg.requests}</div>
          </Card>

          <Card className="p-3">
            <div className={cn("mb-2", isMobile ? "text-sm" : "text-sm")}>Usage Patterns</div>
            <div className="text-xs text-muted-foreground">Peak Day</div>
            <div className="text-base font-medium">{usagePatterns.peakDay || '-'}</div>
            <div className="text-xs text-muted-foreground mt-2">Daily Average</div>
            <div className="text-base font-medium">{formatNumber(usagePatterns.avgDaily)}</div>
            <div className="text-xs mt-2">
              Trend: <span className={usagePatterns.trend==='Rising'?'text-green-600':'text-red-600'}>
                {usagePatterns.trend}
              </span>
            </div>
          </Card>
        </AdaptiveGrid>

        {/* Trends */}
        <AdaptiveGrid
          cols={{ default: 1, lg: 3 }}
          gap={isMobile ? "sm" : "md"}
          className="mb-6"
        >
          <Card className="p-3">
            <div className="text-sm mb-2">Model Request Trend</div>
            <ResponsiveContainer width="100%" height={isMobile ? 120 : 160}>
              <LineChart data={timeSeries}>
                <CartesianGrid strokeOpacity={0.2} vertical={false} />
                <XAxis dataKey="date" tickLine={false} axisLine={false} />
                <YAxis tickLine={false} axisLine={false} width={40} />
                <Tooltip />
                <Line type="monotone" dataKey="requests" stroke="#4318FF" strokeWidth={2} dot={false} />
              </LineChart>
            </ResponsiveContainer>
          </Card>

          <Card className="p-3">
            <div className="text-sm mb-2">Quota Usage Trend</div>
            <ResponsiveContainer width="100%" height={isMobile ? 120 : 160}>
              <LineChart data={timeSeries}>
                <CartesianGrid strokeOpacity={0.2} vertical={false} />
                <XAxis dataKey="date" tickLine={false} axisLine={false} />
                <YAxis tickLine={false} axisLine={false} width={40} />
                <Tooltip />
                <Line type="monotone" dataKey="quota" stroke="#00B5D8" strokeWidth={2} dot={false} />
              </LineChart>
            </ResponsiveContainer>
          </Card>

          <Card className="p-3">
            <div className="text-sm mb-2">Token Usage Trend</div>
            <ResponsiveContainer width="100%" height={isMobile ? 120 : 160}>
              <LineChart data={timeSeries}>
                <CartesianGrid strokeOpacity={0.2} vertical={false} />
                <XAxis dataKey="date" tickLine={false} axisLine={false} />
                <YAxis tickLine={false} axisLine={false} width={40} />
                <Tooltip />
              <Line type="monotone" dataKey="tokens" stroke="#FF5E7D" strokeWidth={2} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        </Card>
      </AdaptiveGrid>

      {/* Token Statistics Chart */}
      <Card className="p-3 mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="text-sm">Statistics - Tokens</div>
        </div>
        <ResponsiveContainer width="100%" height={isMobile ? 200 : 260}>
          <BarChart data={stackedByTokens}>
            <CartesianGrid strokeOpacity={0.2} vertical={false} />
            <XAxis dataKey="date" tickLine={false} axisLine={false} />
            <YAxis tickLine={false} axisLine={false} width={40} />
            <Tooltip />
            <Legend wrapperStyle={{ fontSize: 12 }} height={24} />
            {uniqueModels.map((m, idx) => (
              <Bar key={m} dataKey={m} stackId="tokens" fill={barColor(idx)} />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </Card>

      {/* Model Efficiency Analysis */}
      <Card className="p-3 mb-6">
        <div className="text-sm mb-3">Model Efficiency Analysis</div>
        <div className="overflow-auto">
          <table className={cn("w-full", isMobile ? "text-xs" : "text-sm")}>
            <thead>
              <tr className="text-left text-muted-foreground">
                <th className="py-2 pr-2">#</th>
                <th className="py-2 pr-2">Model</th>
                <th className="py-2 pr-2">Requests</th>
                <th className={cn("py-2 pr-2", isMobile ? "hidden" : "")}>Avg Cost</th>
                <th className={cn("py-2 pr-2", isMobile ? "hidden" : "")}>Avg Tokens</th>
                <th className="py-2 pr-2">Efficiency</th>
              </tr>
            </thead>
            <tbody>
              {efficiency.slice(0, 10).map((m, i) => (
                <tr key={m.model} className="border-t">
                  <td className="py-2 pr-2">{i+1}</td>
                  <td className="py-2 pr-2 font-medium">{m.model}</td>
                  <td className="py-2 pr-2">{formatNumber(m.requests)}</td>
                  <td className={cn("py-2 pr-2", isMobile ? "hidden" : "")}>{m.avgCost.toFixed(4)}</td>
                  <td className={cn("py-2 pr-2", isMobile ? "hidden" : "")}>{Math.round(m.avgTokens)}</td>
                  <td className="py-2 pr-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="h-2 bg-accent rounded"
                        style={{
                          width: Math.min(100, Math.round(m.efficiency / (efficiency[0]?.efficiency || 1) * 100)) + '%'
                        }}
                      />
                      <span className={cn(isMobile ? "text-xs" : "")}>
                        {m.efficiency ? m.efficiency.toFixed(0) : 0}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
              {efficiency.length === 0 && (
                <tr><td className="py-3 text-muted-foreground" colSpan={6}>No data</td></tr>
              )}
          </tbody>
        </table>
      </div>
    </Card>

    {/* Cost Optimization Recommendations */}
    <Card className="p-3 mb-6">
      <div className="text-sm mb-2">Cost Optimization Recommendations</div>
      <ul className={cn("list-disc pl-5 space-y-1", isMobile ? "text-xs" : "text-sm")}>
        {recommendations.map((r, idx) => (<li key={idx}>{r}</li>))}
      </ul>
    </Card>

    {/* Data Table */}
    <Card>
      <CardContent className={cn(isMobile ? "p-4" : "p-6")}>
        <DataTable
          columns={columns}
          data={rows}
          pageIndex={0}
          pageSize={rows.length || 10}
          total={rows.length}
          onPageChange={() => {}}
        />
      </CardContent>
    </Card>
  </ResponsiveSection>
</ResponsivePageContainer>
)
}

export function barColor(i: number) {
  const palette = [
    '#4318FF', '#00B5D8', '#6C63FF', '#05CD99', '#FFB547', '#FF5E7D', '#41B883', '#7983FF', '#FF8F6B', '#49BEFF',
    '#8B5CF6', '#F59E0B', '#EF4444', '#10B981', '#3B82F6',
  ]
  return palette[i % palette.length]
}

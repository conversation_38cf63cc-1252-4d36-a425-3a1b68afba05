import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { api } from '@/lib/api'

// Helper function to render quota with USD conversion
const renderQuotaWithPrompt = (quota: number): string => {
  const quotaPerUnit = parseFloat(localStorage.getItem('quota_per_unit') || '500000')
  const displayInCurrency = localStorage.getItem('display_in_currency') === 'true'

  if (displayInCurrency) {
    const usdValue = (quota / quotaPerUnit).toFixed(6)
    return `${quota.toLocaleString()} tokens ($${usdValue})`
  }
  return `${quota.toLocaleString()} tokens`
}

const userSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  display_name: z.string().optional(),
  password: z.string().optional(),
  email: z.string().email('Valid email is required').optional(),
  quota: z.number().min(0, 'Quota must be non-negative'),
  group: z.string().min(1, 'Group is required'),
})

type UserForm = z.infer<typeof userSchema>

interface Group {
  key: string
  text: string
  value: string
}

export function EditUserPage() {
  const params = useParams()
  const userId = params.id
  const isEdit = userId !== undefined
  const navigate = useNavigate()

  const [loading, setLoading] = useState(isEdit)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [groupOptions, setGroupOptions] = useState<Group[]>([])

  const form = useForm<UserForm>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: '',
      display_name: '',
      password: '',
      email: '',
      quota: 0,
      group: 'default',
    },
  })

  const loadUser = async () => {
    if (!userId) return

    try {
      const response = await api.get(`/user/${userId}`)
      const { success, message, data } = response.data

      if (success && data) {
        form.reset({
          ...data,
          password: '', // Don't pre-fill password for security
        })
      } else {
        throw new Error(message || 'Failed to load user')
      }
    } catch (error) {
      console.error('Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadGroups = async () => {
    try {
      const response = await api.get('/group/')
      const { success, data } = response.data

      if (success && data) {
        const options = data.map((group: string) => ({
          key: group,
          text: group,
          value: group,
        }))
        setGroupOptions(options)
      }
    } catch (error) {
      console.error('Error loading groups:', error)
    }
  }

  useEffect(() => {
    if (isEdit) {
      loadUser()
    } else {
      setLoading(false)
    }
    loadGroups()
  }, [isEdit, userId])

  const onSubmit = async (data: UserForm) => {
    setIsSubmitting(true)
    try {
      let payload = { ...data }

      // Don't send empty password
      if (!payload.password) {
        delete payload.password
      }

      let response
      if (isEdit && userId) {
        response = await api.put('/user/', { ...payload, id: parseInt(userId) })
      } else {
        response = await api.post('/user/', payload)
      }

      const { success, message } = response.data
      if (success) {
        navigate('/users', {
          state: {
            message: isEdit ? 'User updated successfully' : 'User created successfully'
          }
        })
      } else {
        form.setError('root', { message: message || 'Operation failed' })
      }
    } catch (error) {
      form.setError('root', {
        message: error instanceof Error ? error.message : 'Operation failed'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3">Loading user...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>{isEdit ? 'Edit User' : 'Create User'}</CardTitle>
          <CardDescription>
            {isEdit ? 'Update user information' : 'Create a new user account'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="display_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter display name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isEdit ? 'New Password (leave empty to keep current)' : 'Password *'}</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="quota"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Quota {field.value ? `(${renderQuotaWithPrompt(field.value)})` : '(tokens)'}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="group"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Group *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a group" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {groupOptions.map((group) => (
                            <SelectItem key={group.value} value={group.value}>
                              {group.text}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {form.formState.errors.root && (
                <div className="text-sm text-destructive">
                  {form.formState.errors.root.message}
                </div>
              )}

              <div className="flex gap-2">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting
                    ? (isEdit ? 'Updating...' : 'Creating...')
                    : (isEdit ? 'Update User' : 'Create User')
                  }
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/users')}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}

export default EditUserPage
